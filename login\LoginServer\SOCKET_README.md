# Socket.IO Implementation Guide

## Overview

This document describes the improved Socket.IO implementation with authentication, security features, and best practices.

## Features

### 🔐 Security Features
- **JWT Authentication**: All socket connections require valid JWT tokens
- **Rate Limiting**: Prevents spam and DoS attacks (100 requests per minute per user)
- **CORS Protection**: Configurable allowed origins
- **Input Validation**: All incoming data is validated
- **Session Management**: Tracks active users and automatically cleans up inactive sessions

### 🚀 Performance Features
- **Connection Pooling**: Efficient connection management
- **Compression**: HTTP compression for better performance
- **Optimized Transports**: WebSocket preferred, polling fallback
- **Heartbeat Monitoring**: Automatic ping/pong for connection health

### 📡 Real-time Features
- **Room Management**: Users automatically join organization and user-specific rooms
- **Message Broadcasting**: Send messages to specific rooms or entire organization
- **User Presence**: Track online/offline status
- **Logout Handling**: Graceful logout with cleanup

## Socket Events

### Client → Server Events

#### `sendMessage`
Send a message to other users.

```javascript
socket.emit('sendMessage', {
    message: 'Hello everyone!',
    targetRoom: 'org:123', // optional, defaults to user's organization
    messageType: 'text' // optional, defaults to 'text'
}, (response) => {
    console.log(response); // { success: true, messageId: 'msg_123...' }
});
```

#### `logout`
Logout and disconnect from the server.

```javascript
socket.emit('logout', (response) => {
    console.log(response); // { success: true, message: 'Logged out successfully' }
});
```

#### `joinRoom`
Join a specific room.

```javascript
socket.emit('joinRoom', { room: 'org:123' }, (response) => {
    console.log(response); // { success: true, message: 'Joined room: org:123' }
});
```

#### `leaveRoom`
Leave a specific room.

```javascript
socket.emit('leaveRoom', { room: 'org:123' }, (response) => {
    console.log(response); // { success: true, message: 'Left room: org:123' }
});
```

#### `ping`
Check connection health.

```javascript
socket.emit('ping', (response) => {
    console.log(response); // { success: true, message: 'pong', timestamp: '...' }
});
```

### Server → Client Events

#### `connected`
Sent when connection is established and authenticated.

```javascript
socket.on('connected', (data) => {
    console.log(data);
    // {
    //     message: 'Successfully connected to server',
    //     userId: '...',
    //     organizationId: '...',
    //     timestamp: '...'
    // }
});
```

#### `receiveMessage`
Receive a message from another user.

```javascript
socket.on('receiveMessage', (data) => {
    console.log(data);
    // {
    //     id: 'msg_123...',
    //     message: 'Hello everyone!',
    //     messageType: 'text',
    //     sender: {
    //         userId: '...',
    //         email: '<EMAIL>',
    //         firstName: 'John',
    //         lastName: 'Doe'
    //     },
    //     organizationId: '...',
    //     timestamp: '...'
    // }
});
```

#### `userOnline`
Notifies when a user comes online.

```javascript
socket.on('userOnline', (data) => {
    console.log(data);
    // {
    //     userId: '...',
    //     email: '<EMAIL>',
    //     firstName: 'John',
    //     lastName: 'Doe',
    //     timestamp: '...'
    // }
});
```

#### `userOffline`
Notifies when a user goes offline.

```javascript
socket.on('userOffline', (data) => {
    console.log(data);
    // {
    //     userId: '...',
    //     email: '<EMAIL>',
    //     firstName: 'John',
    //     lastName: 'Doe',
    //     reason: 'logout' | 'disconnect',
    //     timestamp: '...'
    // }
});
```

## Client Connection

### Basic Connection

```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:5005', {
    auth: {
        token: 'your-jwt-token-here'
    },
    transports: ['websocket', 'polling']
});
```

### Error Handling

```javascript
socket.on('connect_error', (error) => {
    if (error.message.includes('Authentication')) {
        // Handle authentication error
        console.error('Authentication failed');
        // Redirect to login page
    } else {
        // Handle other connection errors
        console.error('Connection failed:', error.message);
    }
});
```

## Server Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Socket configuration
SOCKET_PORT=8081
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://yourdomain.com

# JWT configuration (already exists)
JWT_SECRET=your-secret-key
```

### CORS Configuration

The server automatically configures CORS based on the `ALLOWED_ORIGINS` environment variable. If not set, it defaults to localhost development URLs.

## Room Structure

### Automatic Rooms
- `org:{organizationId}` - All users in the same organization
- `user:{userId}` - Individual user room for private messages

### Custom Rooms
You can create custom rooms following the pattern:
- `org:{organizationId}:custom-room-name`
- `user:{userId}:private-room`

## Security Considerations

1. **Token Validation**: All connections must provide a valid JWT token
2. **Rate Limiting**: Prevents abuse with configurable limits
3. **Room Access Control**: Users can only join rooms they have access to
4. **Input Sanitization**: All incoming data is validated
5. **Session Cleanup**: Inactive sessions are automatically cleaned up

## Monitoring and Debugging

### Active Users
```javascript
import { getActiveUsers, getUserCountByOrganization } from './socket';

// Get all active users
const activeUsers = getActiveUsers();

// Get user count by organization
const orgCounts = getUserCountByOrganization();
```

### Logging
The server logs all important socket events:
- User connections/disconnections
- Message sending
- Room joins/leaves
- Authentication failures
- Errors

## Best Practices

1. **Always handle callbacks** for acknowledgment-based events
2. **Implement reconnection logic** on the client side
3. **Validate data** before sending to the server
4. **Handle authentication errors** gracefully
5. **Use rooms efficiently** to reduce unnecessary broadcasts
6. **Monitor connection health** with periodic pings
7. **Implement proper error handling** for all events

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check if JWT token is valid and not expired
   - Ensure token is passed in the `auth.token` field

2. **Connection Timeout**
   - Check network connectivity
   - Verify server is running and accessible
   - Check CORS configuration

3. **Rate Limit Exceeded**
   - Reduce message frequency
   - Implement client-side rate limiting

4. **Room Access Denied**
   - Verify user has access to the organization/room
   - Check room naming convention

For more examples, see the `examples/socket-client-example.js` file.

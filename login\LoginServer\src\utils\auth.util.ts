import jwt from "jsonwebtoken";
import bcrypt from "bcrypt";
import { Request, Response, NextFunction } from "express";
import { getUser } from "../controllers/auth.controller";
import { User } from "../models/auth.model";

const JWT_SECRET = process.env.JWT_SECRET ?? "RPS_cloud_PrivateKey8956123";
const JWT_EXPIRES = "4h";
const JWTREFRESH_EXPIRES = "7d";

export interface AuthenticatedRequest extends Request {
  user?: User;
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

export function generateJWT(payload: {
  userId: string;
  allowedApplications: any[];
  email: string;
  organizationId: string;
  roles: any[];
  firstName: string;
  lastName: string;
  permissionList: any[],
  uniqSocketId: string
}): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES });
}

export function generateRefreshJWT(payload: {
  userId: string;
  allowedApplications: [];
  email: string;
  organizationId: string;
  firstName?: string;
  lastName?: string;
}): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWTREFRESH_EXPIRES });
}

export function verifyJWT(token: string): {
  userId: string;
  email: string;
  permissions: [];
  firstName?: string;
  lastName?: string;
} | null {
  try {
    return jwt.verify(token, JWT_SECRET) as {
      userId: string;
      email: string;
      permissions: [];
      firstName?: string;
      lastName?: string;
    };
  } catch {
    return null;
  }
}

export async function authenticateToken(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(" ")[1];

  if (!token) {
    res.status(401).json({ message: "Access token required" });
    return;
  }

  try {
    const decoded = verifyJWT(token);
    if (!decoded) {
      res.status(403).json({ message: "Invalid token" });
      return;
    }

    const user = await getUser(decoded.userId);
    if (!user) {
      res.status(403).json({ message: "User not found" });
      return;
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(403).json({ message: "Invalid token" });
  }
}

export function generateRandomToken(length: number = 32): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export async function validateToken(authToken: string): Promise<boolean> {
  if (!authToken) {
    return false;
  }

  try {
    const decoded = jwt.verify(authToken, JWT_SECRET);
    if (!decoded) {
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
}

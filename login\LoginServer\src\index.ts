import express from "express";
import cors, { CorsOptionsDelegate, CorsRequest } from "cors";
import authRoutes from "./routes/auth.route";
import { Server as SocketIO } from "socket.io";
import { db } from "./config/db";
import dotenv from "dotenv";
import { initSocket } from "./socket";

dotenv.config();

const app = express();
const PORT = parseInt(process.env.PORT || "5005", 10);


app.use(cors());


app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Socket.IO configuration with security and performance optimizations
const socketOptions: any = {
    allowEIO3: true,
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 30000,
    maxHttpBufferSize: 1e6, // 1MB
    transports: ['websocket', 'polling'],
    allowUpgrades: true,
    compression: true,
    httpCompression: {
        threshold: 1024,
        chunkSize: 1024,
        windowBits: 13,
        concurrencyLimit: 10
    }
};

const server = require("http").createServer(app);
const Socket_io = new SocketIO(server, socketOptions);

initSocket(Socket_io);


// Health check endpoint
app.get("/health", (_req, res) => {
    res.json({ status: "OK", timestamp: new Date().toISOString() });
});

// API routes
app.use("/api/auth", authRoutes);

// Register API routes
async function startServer() {
    try {
        // Ensure MongoDB connection is established
        await db.initialize();

        // Start the HTTP server with Socket.IO attached
        const httpServer = server.listen(PORT, "0.0.0.0", () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
            console.log(`🔌 Socket.IO server running on port ${PORT}`);
            console.log(`📊 MongoDB Atlas connected successfully`);
            console.log(`🔒 CORS enabled for origins: ${socketOptions.cors.origin.join(', ')}`);
        });

        // Graceful shutdown
        const gracefulShutdown = (signal: string) => {
            console.log(`${signal} received, shutting down gracefully`);

            // Close Socket.IO server
            Socket_io.close(() => {
                console.log('Socket.IO server closed');
            });

            // Close HTTP server
            httpServer.close(() => {
                console.log("HTTP server closed");
                process.exit(0);
            });

            // Force close after 10 seconds
            setTimeout(() => {
                console.error("Could not close connections in time, forcefully shutting down");
                process.exit(1);
            }, 10000);
        };

        process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
        process.on("SIGINT", () => gracefulShutdown("SIGINT"));

    } catch (error) {
        console.error("Failed to start server:", error);
        process.exit(1);
    }
}

startServer();

import express from "express";
import cors, { CorsOptionsDelegate, CorsRequest } from "cors";
import authRoutes from "./routes/auth.route";
import { Server as SocketIO } from "socket.io";
import { db } from "./config/db";
import dotenv from "dotenv";
import { initSocket } from "./socket";

dotenv.config();

const app = express();
const PORT = parseInt(process.env.PORT || "5005", 10);


app.use(cors());


app.use(express.json());
app.use(express.urlencoded({ extended: true }));

const options: any = {
    allowEIO3: true,
};

const server = require("http").createServer(app);
const Socket_io = new SocketIO(server, options);

initSocket(Socket_io);


// Health check endpoint
app.get("/health", (req, res) => {
    res.json({ status: "OK", timestamp: new Date().toISOString() });
});

// API routes
app.use("/api/auth", authRoutes);

// Register API routes
async function startServer() {
    try {
        // Ensure MongoDB connection is established
        await db.initialize();

        const server = app.listen(PORT, "0.0.0.0", () => {
            console.log(`Server running on http://localhost:${PORT}`);
            console.log(`MongoDB Atlas connected successfully`);
        });

        // Graceful shutdown
        process.on("SIGTERM", () => {
            console.log("SIGTERM received, shutting down gracefully");
            server.close(() => {
                console.log("Process terminated");
                process.exit(0);
            });
        });
    } catch (error) {
        console.error("Failed to start server:", error);
        process.exit(1);
    }
}
Socket_io.listen(Number(process.env.SOCKET_PORT));

startServer();

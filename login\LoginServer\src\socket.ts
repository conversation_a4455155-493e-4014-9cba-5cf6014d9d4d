import { Server, Socket } from "socket.io";

export function initSocket(io: Server) {
    io.on("connection", (socket: Socket) => {
        console.log("🟢 User connected:", socket.id);

        socket.on("sendMessage", (data: string) => {
            console.log("📩 Message received:", data);
            io.emit("receiveMessage", data);
        });

        socket.on("disconnect", () => {
            console.log("🔴 User disconnected:", socket.id);
        });
    });
}

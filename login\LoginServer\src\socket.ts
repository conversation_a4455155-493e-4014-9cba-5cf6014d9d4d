import { Server, Socket } from "socket.io";
import { verifyJWT } from "./utils/auth.util";
import { getUser } from "./controllers/auth.controller";
import { User } from "./models/auth.model";

interface AuthenticatedSocket extends Socket {
    user?: User;
    userId?: string;
    organizationId?: string;
}



// Store active user sessions
const activeUsers = new Map<string, {
    socketId: string;
    userId: string;
    organizationId: string;
    connectedAt: Date;
    lastActivity: Date;
}>();

// Socket authentication middleware
const authenticateSocket = async (socket: AuthenticatedSocket, next: (err?: Error) => void) => {
    try {
        const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization?.split(' ')[1];

        if (!token) {
            return next(new Error('Authentication token required'));
        }

        const decoded = verifyJWT(token);
        if (!decoded) {
            return next(new Error('Invalid authentication token'));
        }

        const user = await getUser(decoded.userId);
        if (!user) {
            return next(new Error('User not found'));
        }

        // Attach user data to socket
        socket.user = user;
        socket.userId = user._id.toString();
        socket.organizationId = user.organizationId;

        next();
    } catch (error) {
        console.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
    }
};

// Rate limiting middleware
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 100;

const rateLimitMiddleware = (socket: AuthenticatedSocket, next: (err?: Error) => void) => {
    const clientId = socket.userId || socket.id;
    const now = Date.now();

    const clientData = rateLimitMap.get(clientId);

    if (!clientData || now > clientData.resetTime) {
        rateLimitMap.set(clientId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
        return next();
    }

    if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
        return next(new Error('Rate limit exceeded'));
    }

    clientData.count++;
    next();
};

export function initSocket(io: Server) {
    // Apply authentication middleware
    io.use(authenticateSocket);

    // Apply rate limiting middleware
    io.use(rateLimitMiddleware);

    io.on("connection", (socket: AuthenticatedSocket) => {
        const userId = socket.userId!;
        const organizationId = socket.organizationId!;

        console.log(`🟢 User connected: ${socket.user?.email} (${socket.id})`);

        // Store active user session
        activeUsers.set(socket.id, {
            socketId: socket.id,
            userId,
            organizationId,
            connectedAt: new Date(),
            lastActivity: new Date()
        });

        // Join user to their organization room
        socket.join(`org:${organizationId}`);
        socket.join(`user:${userId}`);

        // Send connection confirmation
        socket.emit("connected", {
            message: "Successfully connected to server",
            userId,
            organizationId,
            timestamp: new Date().toISOString()
        });

        // Broadcast to organization that user is online
        socket.to(`org:${organizationId}`).emit("userOnline", {
            userId,
            email: socket.user?.email,
            firstName: socket.user?.firstName,
            lastName: socket.user?.lastName,
            timestamp: new Date().toISOString()
        });

        // Handle incoming messages with validation
        socket.on("sendMessage", (data: any, callback?: (response: any) => void) => {
            try {
                // Update last activity
                const userSession = activeUsers.get(socket.id);
                if (userSession) {
                    userSession.lastActivity = new Date();
                }

                // Validate message data
                if (!data || typeof data !== 'object') {
                    const error = { success: false, message: "Invalid message format" };
                    if (callback) callback(error);
                    return;
                }

                const { message, targetRoom, messageType = 'text' } = data;

                if (!message || message.trim().length === 0) {
                    const error = { success: false, message: "Message cannot be empty" };
                    if (callback) callback(error);
                    return;
                }

                console.log(`📩 Message from ${socket.user?.email}:`, message);

                const messageData = {
                    id: generateMessageId(),
                    message: message.trim(),
                    messageType,
                    sender: {
                        userId,
                        email: socket.user?.email,
                        firstName: socket.user?.firstName,
                        lastName: socket.user?.lastName
                    },
                    organizationId,
                    timestamp: new Date().toISOString()
                };

                // Send to specific room or organization
                const room = targetRoom || `org:${organizationId}`;
                socket.to(room).emit("receiveMessage", messageData);

                // Send confirmation back to sender
                if (callback) {
                    callback({
                        success: true,
                        message: "Message sent successfully",
                        messageId: messageData.id
                    });
                }

            } catch (error) {
                console.error('Error handling message:', error);
                if (callback) {
                    callback({ success: false, message: "Failed to send message" });
                }
            }
        });

        // Handle logout event
        socket.on("logout", (callback?: (response: any) => void) => {
            try {
                console.log(`🚪 User logout: ${socket.user?.email} (${socket.id})`);

                // Broadcast to organization that user is going offline
                socket.to(`org:${organizationId}`).emit("userOffline", {
                    userId,
                    email: socket.user?.email,
                    firstName: socket.user?.firstName,
                    lastName: socket.user?.lastName,
                    reason: "logout",
                    timestamp: new Date().toISOString()
                });

                // Remove from active users
                activeUsers.delete(socket.id);

                // Send confirmation
                if (callback) {
                    callback({
                        success: true,
                        message: "Logged out successfully"
                    });
                }

                // Disconnect the socket
                socket.disconnect(true);

            } catch (error) {
                console.error('Error handling logout:', error);
                if (callback) {
                    callback({ success: false, message: "Logout failed" });
                }
            }
        });

        // Handle join room event
        socket.on("joinRoom", (data: { room: string }, callback?: (response: any) => void) => {
            try {
                const { room } = data;

                if (!room || typeof room !== 'string') {
                    if (callback) callback({ success: false, message: "Invalid room name" });
                    return;
                }

                // Validate room access (you can add more complex logic here)
                if (!room.startsWith(`org:${organizationId}`) && !room.startsWith(`user:${userId}`)) {
                    if (callback) callback({ success: false, message: "Access denied to room" });
                    return;
                }

                socket.join(room);
                console.log(`👥 User ${socket.user?.email} joined room: ${room}`);

                if (callback) {
                    callback({ success: true, message: `Joined room: ${room}` });
                }

            } catch (error) {
                console.error('Error joining room:', error);
                if (callback) {
                    callback({ success: false, message: "Failed to join room" });
                }
            }
        });

        // Handle leave room event
        socket.on("leaveRoom", (data: { room: string }, callback?: (response: any) => void) => {
            try {
                const { room } = data;

                if (!room || typeof room !== 'string') {
                    if (callback) callback({ success: false, message: "Invalid room name" });
                    return;
                }

                socket.leave(room);
                console.log(`👋 User ${socket.user?.email} left room: ${room}`);

                if (callback) {
                    callback({ success: true, message: `Left room: ${room}` });
                }

            } catch (error) {
                console.error('Error leaving room:', error);
                if (callback) {
                    callback({ success: false, message: "Failed to leave room" });
                }
            }
        });

        // Handle ping for connection health check
        socket.on("ping", (callback?: (response: any) => void) => {
            const userSession = activeUsers.get(socket.id);
            if (userSession) {
                userSession.lastActivity = new Date();
            }

            if (callback) {
                callback({
                    success: true,
                    message: "pong",
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Handle disconnect
        socket.on("disconnect", (reason: string) => {
            console.log(`🔴 User disconnected: ${socket.user?.email} (${socket.id}) - Reason: ${reason}`);

            // Broadcast to organization that user is offline
            socket.to(`org:${organizationId}`).emit("userOffline", {
                userId,
                email: socket.user?.email,
                firstName: socket.user?.firstName,
                lastName: socket.user?.lastName,
                reason,
                timestamp: new Date().toISOString()
            });

            // Remove from active users
            activeUsers.delete(socket.id);
        });

        // Handle connection errors
        socket.on("error", (error: Error) => {
            console.error(`❌ Socket error for user ${socket.user?.email}:`, error.message);

            // Optionally disconnect on certain errors
            if (error.message.includes('Authentication') || error.message.includes('Rate limit')) {
                socket.disconnect(true);
            }
        });
    });

    // Clean up inactive sessions periodically
    setInterval(() => {
        const now = new Date();
        const INACTIVE_THRESHOLD = 30 * 60 * 1000; // 30 minutes

        for (const [socketId, session] of activeUsers.entries()) {
            if (now.getTime() - session.lastActivity.getTime() > INACTIVE_THRESHOLD) {
                console.log(`🧹 Cleaning up inactive session: ${session.userId}`);
                activeUsers.delete(socketId);

                // Disconnect the socket if it still exists
                const socket = io.sockets.sockets.get(socketId);
                if (socket) {
                    socket.disconnect(true);
                }
            }
        }
    }, 5 * 60 * 1000); // Check every 5 minutes
}

// Helper function to generate unique message IDs
function generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// Export function to get active users (useful for admin purposes)
export function getActiveUsers() {
    return Array.from(activeUsers.values());
}

// Export function to get user count by organization
export function getUserCountByOrganization(): Map<string, number> {
    const orgCounts = new Map<string, number>();

    for (const session of activeUsers.values()) {
        const count = orgCounts.get(session.organizationId) || 0;
        orgCounts.set(session.organizationId, count + 1);
    }

    return orgCounts;
}

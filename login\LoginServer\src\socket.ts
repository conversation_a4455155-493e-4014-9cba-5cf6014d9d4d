import { Server, Socket } from "socket.io";

interface UserSocket extends Socket {
    userId?: string;
}

// Store active user sessions
const activeUsers = new Map<string, {
    socketId: string;
    userId: string;
    connectedAt: Date;
}>();

export function initSocket(io: Server) {
    io.on("connection", (socket: UserSocket) => {
        console.log("🟢 User connected:", socket.id);

        // Handle user joining with userId from client
        socket.on("join", (data: { userId: string }, callback?: (response: any) => void) => {
            try {
                const { userId } = data;

                if (!userId || typeof userId !== 'string') {
                    if (callback) callback({ success: false, message: "Invalid user ID" });
                    return;
                }

                // Store user session
                socket.userId = userId;
                activeUsers.set(socket.id, {
                    socketId: socket.id,
                    userId,
                    connectedAt: new Date()
                });

                // Join user to their personal room
                socket.join(`user:${userId}`);

                console.log(`👤 User ${userId} joined (${socket.id})`);

                if (callback) {
                    callback({
                        success: true,
                        message: "Successfully joined",
                        userId
                    });
                }

                // Emit to all clients that user is online
                io.emit("userOnline", {
                    userId,
                    timestamp: new Date().toISOString()
                });

            } catch (error) {
                console.error('Error handling join:', error);
                if (callback) {
                    callback({ success: false, message: "Failed to join" });
                }
            }
        });

        // Handle logout event
        socket.on("logout", (callback?: (response: any) => void) => {
            try {
                const userId = socket.userId;

                if (!userId) {
                    if (callback) callback({ success: false, message: "User not identified" });
                    return;
                }

                console.log(`🚪 User logout: ${userId} (${socket.id})`);

                // Emit to all clients that user is offline
                io.emit("userLogout", {
                    userId,
                    reason: "logout",
                    timestamp: new Date().toISOString()
                });

                // Remove from active users
                activeUsers.delete(socket.id);

                // Send confirmation
                if (callback) {
                    callback({
                        success: true,
                        message: "Logged out successfully"
                    });
                }

                // Disconnect the socket
                socket.disconnect(true);

            } catch (error) {
                console.error('Error handling logout:', error);
                if (callback) {
                    callback({ success: false, message: "Logout failed" });
                }
            }
        });

        // Handle disconnect
        socket.on("disconnect", (reason: string) => {
            const userId = socket.userId;

            console.log(`🔴 User disconnected: ${userId || 'unknown'} (${socket.id}) - Reason: ${reason}`);

            if (userId) {
                // Emit to all clients that user is offline
                io.emit("userLogout", {
                    userId,
                    reason: "disconnect",
                    timestamp: new Date().toISOString()
                });

                // Remove from active users
                activeUsers.delete(socket.id);
            }
        });

        // Handle connection errors
        socket.on("error", (error: Error) => {
            console.error(`❌ Socket error:`, error.message);
        });
    });
}

// Export function to get active users
export function getActiveUsers() {
    return Array.from(activeUsers.values());
}

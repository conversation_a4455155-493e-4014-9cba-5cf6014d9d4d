{"version": 3, "file": "find_cursor.js", "sourceRoot": "", "sources": ["../../src/cursor/find_cursor.ts"], "names": [], "mappings": ";;;AACA,+DAAiE;AACjE,oCAA8F;AAC9F,wCAMoB;AAIpB,+CAAwE;AACxE,uEAAmE;AACnE,6CAAqE;AAGrE,kCAAoE;AACpE,oCAA6F;AAG7F,uCAAuC;AAC1B,QAAA,KAAK,GAAG;IACnB,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,SAAS;IACT,SAAS;CACD,CAAC;AAEX,cAAc;AACd,MAAa,UAA0B,SAAQ,2BAA0B;IAQvE,gBAAgB;IAChB,YACE,MAAmB,EACnB,SAA2B,EAC3B,SAAmB,EAAE,EACrB,UAAmC,EAAE;QAErC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAZpC,gBAAgB;QACR,gBAAW,GAAG,CAAC,CAAC;QAatB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QAE3B,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAED,KAAK;QACH,MAAM,aAAa,GAAG,IAAA,oBAAY,EAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACzD,OAAO,aAAa,CAAC,OAAO,CAAC;QAC7B,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YACpE,GAAG,aAAa;SACjB,CAAC,CAAC;IACL,CAAC;IAEQ,GAAG,CAAI,SAA8B;QAC5C,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAkB,CAAC;IAC/C,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,WAAW,CAAC,OAAsB;QACtC,MAAM,OAAO,GAAG;YACd,GAAG,IAAI,CAAC,WAAW,EAAE,uDAAuD;YAC5E,GAAG,IAAI,CAAC,aAAa;YACrB,OAAO;YACP,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;QAEF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC;gBACH,IAAA,uCAA6B,EAAC,OAAO,EAAE,iBAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;YACvE,CAAC;YAAC,MAAM,CAAC;gBACP,MAAM,IAAI,qBAAa,CACrB,gFAAgF,CACjF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,oBAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAEpF,MAAM,QAAQ,GAAG,MAAM,IAAA,oCAAgB,EAAC,IAAI,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAEzF,yDAAyD;QACzD,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC;QAEtC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAC7D,CAAC;IAED,gBAAgB;IACP,KAAK,CAAC,OAAO,CAAC,SAAiB;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,WAAW,EAAE,CAAC;YAChB,oEAAoE;YACpE,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;YACrC,SAAS;gBACP,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,WAAW,GAAG,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;YAE1F,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;gBACnB,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;gBACrB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC;oBACnB,yFAAyF;oBACzF,8FAA8F;oBAC9F,8FAA8F;oBAC9F,6FAA6F;oBAC7F,8FAA8F;oBAC9F,8CAA8C;oBAC9C,mGAAmG;oBACnG,mBAAmB;gBACrB,CAAC;gBACD,OAAO,0BAAc,CAAC,YAAY,CAAC;YACrC,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChD,2FAA2F;QAC3F,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC;QAEzD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK,CAAC,OAAsB;QAChC,IAAA,uBAAe,EACb,kKAAkK,CACnK,CAAC;QACF,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,IAAI,iCAAyB,CAAC,kCAAkC,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,MAAM,IAAA,oCAAgB,EAC3B,IAAI,CAAC,MAAM,EACX,IAAI,sBAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YACpD,GAAG,IAAI,CAAC,WAAW,EAAE,uDAAuD;YAC5E,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,OAAO;SACX,CAAC,CACH,CAAC;IACJ,CAAC;IAUD,KAAK,CAAC,OAAO,CACX,SAAiF,EACjF,OAAgC;QAEhC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEnF,OAAO,CACL,MAAM,IAAA,oCAAgB,EACpB,IAAI,CAAC,MAAM,EACX,IAAI,oBAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YACnD,GAAG,IAAI,CAAC,WAAW,EAAE,uDAAuD;YAC5E,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,OAAO;YACV,OAAO,EAAE,OAAO,IAAI,IAAI;SACzB,CAAC,CACH,CACF,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;IACvC,CAAC;IAED,2BAA2B;IAC3B,MAAM,CAAC,MAAgB;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,IAAU;QACb,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAa;QACf,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,GAAa;QACf,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAC,KAAc;QACtB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,KAAc;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,IAAY,EAAE,KAA2C;QACxE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpB,MAAM,IAAI,iCAAyB,CAAC,GAAG,IAAI,gCAAgC,CAAC,CAAC;QAC/E,CAAC;QAED,iBAAiB;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE7B,wCAAwC;QACxC,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,SAAS;gBACZ,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,KAA0B,CAAC;gBACtD,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,KAAgB,CAAC;gBAC5C,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,KAA0B,CAAC;gBACnD,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,KAAiB,CAAC;gBACzC,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAe,CAAC;gBAC7C,MAAM;YAER,KAAK,KAAK;gBACR,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,KAAiB,CAAC;gBACzC,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,KAA0B,CAAC,CAAC;gBAC/D,MAAM;YAER,KAAK,OAAO;gBACV,IAAI,CAAC,YAAY,GAAG,KAAiB,CAAC;gBACtC,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAgB,CAAC;gBAC9C,MAAM;YAER,KAAK,aAAa;gBAChB,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,KAAgB,CAAC;gBACjD,MAAM;YAER;gBACE,MAAM,IAAI,iCAAyB,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,OAAO,CAAC,KAAa;QACnB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,KAAa;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,iCAAyB,CAAC,8CAA8C,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,KAAK,CAAC;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACM,SAAS,CAAC,KAAa;QAC9B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,iCAAyB,CAAC,yCAAyC,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,OAAO,CAAgC,KAAe;QACpD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,KAAK,CAAC;QACpC,OAAO,IAAgC,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACH,IAAI,CAAC,IAAmB,EAAE,SAAyB;QACjD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,gCAAwB,CAAC,0CAA0C,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAA,iBAAU,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,KAAK,GAAG,IAAI;QACvB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,iCAAyB,CAAC,qDAAqD,CAAC,CAAC;QAC7F,CAAC;QAED,oFAAoF;QACpF,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,KAAK,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAC,KAAuB;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAa;QACjB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,gCAAwB,CAAC,wCAAwC,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,iCAAyB,CAAC,uCAAuC,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,KAAa;QAChB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,gCAAwB,CAAC,uCAAuC,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,IAAI,iCAAyB,CAAC,sCAAsC,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,KAAK,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAlcD,gCAkcC"}
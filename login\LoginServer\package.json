{"name": "api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "tsc && node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "esbuild src/index.ts --platform=node --packages=external --bundle --format=esm --outfile=dist/cloud_loginapi.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "nodemailer": "^7.0.3", "qrcode": "^1.5.4", "socket.io": "^4.8.1", "speakeasy": "^2.0.0", "zod": "^3.25.67"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/mongodb": "^4.0.6", "@types/node": "^24.0.14", "@types/speakeasy": "^2.0.10", "esbuild": "^0.25.5", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}
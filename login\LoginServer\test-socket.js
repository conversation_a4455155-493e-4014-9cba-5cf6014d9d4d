/**
 * Simple Socket.IO Test Script
 * Run this with: node test-socket.js
 * Make sure your server is running first!
 */

const io = require('socket.io-client');

// You'll need to replace this with a valid JWT token from your login endpoint
const TEST_TOKEN = 'your-jwt-token-here';
const SERVER_URL = 'http://localhost:5005';

console.log('🧪 Starting Socket.IO Test...');
console.log('📝 Make sure to replace TEST_TOKEN with a valid JWT token');
console.log('🚀 Make sure your server is running on', SERVER_URL);
console.log('');

// Create socket connection
const socket = io(SERVER_URL, {
    auth: {
        token: TEST_TOKEN
    },
    transports: ['websocket', 'polling'],
    timeout: 10000
});

// Connection events
socket.on('connect', () => {
    console.log('✅ Connected to server with ID:', socket.id);
});

socket.on('connected', (data) => {
    console.log('🎉 Server confirmed connection:', data);
    
    // Test sending a message after successful connection
    setTimeout(() => {
        testSendMessage();
    }, 1000);
    
    // Test ping after 2 seconds
    setTimeout(() => {
        testPing();
    }, 2000);
    
    // Test logout after 5 seconds
    setTimeout(() => {
        testLogout();
    }, 5000);
});

socket.on('connect_error', (error) => {
    console.error('❌ Connection failed:', error.message);
    
    if (error.message.includes('Authentication')) {
        console.log('💡 Tip: Make sure to replace TEST_TOKEN with a valid JWT token');
        console.log('💡 You can get a token by logging in through your API');
    }
    
    process.exit(1);
});

socket.on('disconnect', (reason) => {
    console.log('🔌 Disconnected:', reason);
    process.exit(0);
});

// Message events
socket.on('receiveMessage', (data) => {
    console.log('📨 Received message:', data);
});

// User presence events
socket.on('userOnline', (data) => {
    console.log('🟢 User came online:', data.email);
});

socket.on('userOffline', (data) => {
    console.log('🔴 User went offline:', data.email, '- Reason:', data.reason);
});

// Error handling
socket.on('error', (error) => {
    console.error('⚠️ Socket error:', error);
});

// Test functions
function testSendMessage() {
    console.log('📤 Testing message sending...');
    
    socket.emit('sendMessage', {
        message: 'Hello from test script!',
        messageType: 'text'
    }, (response) => {
        if (response.success) {
            console.log('✅ Message sent successfully:', response.messageId);
        } else {
            console.error('❌ Failed to send message:', response.message);
        }
    });
}

function testPing() {
    console.log('🏓 Testing ping...');
    
    socket.emit('ping', (response) => {
        if (response.success) {
            console.log('✅ Ping successful:', response.message);
        } else {
            console.error('❌ Ping failed:', response.message);
        }
    });
}

function testLogout() {
    console.log('🚪 Testing logout...');
    
    socket.emit('logout', (response) => {
        if (response.success) {
            console.log('✅ Logout successful:', response.message);
        } else {
            console.error('❌ Logout failed:', response.message);
        }
    });
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted by user');
    socket.disconnect();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Test terminated');
    socket.disconnect();
    process.exit(0);
});

console.log('⏳ Attempting to connect...');
console.log('💡 Press Ctrl+C to stop the test');

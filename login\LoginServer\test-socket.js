/**
 * Simple Socket.IO Test Script
 * Run this with: node test-socket.js
 * Make sure your server is running first!
 */

const io = require('socket.io-client');

// Replace this with your user ID
const TEST_USER_ID = 'testuser123';
const SERVER_URL = 'http://localhost:5005';

console.log('🧪 Starting Socket.IO Test...');
console.log('📝 Using test user ID:', TEST_USER_ID);
console.log('🚀 Make sure your server is running on', SERVER_URL);
console.log('');

// Create socket connection
const socket = io(SERVER_URL, {
    transports: ['websocket', 'polling'],
    timeout: 10000
});

// Connection events
socket.on('connect', () => {
    console.log('✅ Connected to server with ID:', socket.id);

    // Join with user ID after connection
    setTimeout(() => {
        testJoin();
    }, 1000);

    // Test logout after 3 seconds
    setTimeout(() => {
        testLogout();
    }, 3000);
});

socket.on('connect_error', (error) => {
    console.error('❌ Connection failed:', error.message);
    process.exit(1);
});

socket.on('disconnect', (reason) => {
    console.log('🔌 Disconnected:', reason);
    process.exit(0);
});

// User presence events
socket.on('userOnline', (data) => {
    console.log('🟢 User came online:', data.userId);
});

socket.on('userOffline', (data) => {
    console.log('🔴 User went offline:', data.userId, '- Reason:', data.reason);
});

// Error handling
socket.on('error', (error) => {
    console.error('⚠️ Socket error:', error);
});

// Test functions
function testJoin() {
    console.log('👤 Testing join with user ID...');

    socket.emit('join', { userId: TEST_USER_ID }, (response) => {
        if (response.success) {
            console.log('✅ Join successful:', response);
        } else {
            console.error('❌ Join failed:', response.message);
        }
    });
}

function testLogout() {
    console.log('🚪 Testing logout...');

    socket.emit('logout', (response) => {
        if (response.success) {
            console.log('✅ Logout successful:', response.message);
        } else {
            console.error('❌ Logout failed:', response.message);
        }
    });
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted by user');
    socket.disconnect();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Test terminated');
    socket.disconnect();
    process.exit(0);
});

console.log('⏳ Attempting to connect...');
console.log('💡 Press Ctrl+C to stop the test');

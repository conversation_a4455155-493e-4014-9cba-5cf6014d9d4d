import { Request, Response } from "express";
import jwt from "jsonwebtoken";
import {
  hashPassword,
  verifyPassword,
  generateJWT,
  AuthenticatedRequest,
  authenticateToken,
  validateToken,
  generateRefreshJWT,
} from "../utils/auth.util";
import { sendOTPEmail, sendPasswordResetEmail } from "../utils/email.util";
import {
  generateOTPCode,
  generateTOTPSecret,
  verifyTOTP,
  generateQRCode,
  generateTemporaryPassword,
} from "../utils/otp.util";
import {
  loginSchema,
  otpVerificationSchema,
  forgotPasswordSchema,
} from "../validations/auth.validation";
import { db } from "../config/db";
import { OTPCode, OTPCodeType, User } from "../models/auth.model";
import { ObjectId } from "mongodb";
import { userTokens } from "src/models/userToken.model";

/**
 * Removes permissionsList and description fields from role objects.
 */
function transformRoles(roles: any[] = []) {
  return roles.map(({ permissionsList, description, ...rest }) => rest);
}

/**
 * Groups permissions into a { resource: [actions] } object.
 */
function buildPermissionList(roles: any[] = [], selectedPermissions: any[] = []) {
  // Merge permissions from roles and selectedPermissions
  const rolePermissions = roles.flatMap((role) => role.permissionsList || []);
  const allPermissions = [...rolePermissions, ...(selectedPermissions || [])];

  const resourceMap = new Map<string, Set<string>>();

  for (const permission of allPermissions) {
    const resource = permission?.resource;
    const action = permission?.action;

    if (resource && action) {
      if (!resourceMap.has(resource)) {
        resourceMap.set(resource, new Set());
      }
      resourceMap.get(resource)?.add(action);
    }
  }

  // Convert Map → Object { resource: [actions] }
  const permissionList: Record<string, string[]> = {};
  for (const [resource, actionsSet] of resourceMap.entries()) {
    permissionList[resource] = Array.from(actionsSet);
  }

  return permissionList;
}


export const getUserById = async (_id: string) => {
  try {
    return await db["connection"]
      .getUsersCollection()
      .findOne({ _id: ObjectId.createFromHexString(_id) });
  } catch (error) {
    return null;
  }
};

export const getUserByUsername = async (username: string) => {
  try {
    return await db["connection"].getUsersCollection().findOne({ username });
  } catch (error) {
    return null;
  }
};

export const getUserByEmail = async (email: string) => {
  try {
    return await db["connection"].getUsersCollection().findOne({ email });
  } catch (error) {
    return null;
  }
};

export const updateUser = async (
  id: ObjectId,
  updates: Partial<User>
): Promise<User | null> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: new Date(),
    };
    const result = await db["connection"]
      .getUsersCollection()
      .findOneAndUpdate(
        { _id: id },
        { $set: updateData },
        { returnDocument: "after" }
      );

    if (!result?._id) return null;

    return result as User;
  } catch (error) {
    return null;
  }
};

export const createOTPCode = async (
  userId: ObjectId,
  code: string,
  type: OTPCodeType,
  expiresAt: Date
): Promise<OTPCode | null> => {
  try {
    const otpCode: OTPCode = {
      _id: new ObjectId().toString(),
      userId,
      code,
      type,
      expiresAt,
      used: false,
      createdAt: new Date(),
    };

    await db["connection"].getOTPCodesCollection().insertOne(otpCode);
    return otpCode;
  } catch (error) {
    return null;
  }
};

export const updateOTPCode = async (
  userId: ObjectId,
  code: string,
  type: OTPCodeType,
  expiresAt: Date
): Promise<OTPCode | null> => {
  try {
    const updateData = {
      userId,
      code,
      type,
      expiresAt,
      used: false,
      updatedAt: new Date(),
    };

    const result = await db["connection"]
      .getOTPCodesCollection()
      .findOneAndUpdate(
        { userId: userId },
        { $set: updateData },
        { returnDocument: "after" }
      );

    return result as OTPCode | null;
  } catch (error) {
    return null;
  }
};

export const getUser = async (_id: string): Promise<User | null> => {
  try {
    const result = await db["connection"]
      .getUsersCollection()
      .findOne({ _id: new ObjectId(_id) });

    if (!result?._id) return null;
    return result as User;
  } catch (error) {
    console.log("error", error);
    return null;
  }
};

export const getValidOTPCode = async (
  userId: ObjectId,
  code: string,
  type: OTPCodeType,
  checkUsed: boolean
): Promise<OTPCode | null> => {
  try {
    const result = await db["connection"].getOTPCodesCollection().findOne({
      userId: new ObjectId(userId.toString()),
      code: code,
      type,
      used: checkUsed,
    });

    return result as OTPCode | null;
  } catch (error) {
    return null;
  }
};

export const markOTPCodeUsed = async (_id: string): Promise<OTPCode | null> => {
  try {
    const updates = {
      used: true,
    };

    const result = await db["connection"]
      .getOTPCodesCollection()
      .findOneAndUpdate(
        { _id },
        { $set: updates },
        { returnDocument: "after" }
      );

    return result as OTPCode | null;
  } catch (error) {
    return null;
  }
};

const getUserPayload = async (
  roles: any[],
  uniquePermissions: any[],
  user?: User
) => {
  return {
    id: user?._id?.toString(),
    email: user?.email,
    firstName: user?.firstName,
    lastName: user?.lastName,
    organizationId: user?.organizationId,
    allowedApplications: user?.allowedApplications,
    roles: roles,
    permissionList: uniquePermissions,
  };
};

const createToken = async (user: User, isLogin: boolean = false) => {
  // Collect all permissions from all roles

  const rolesWithoutPermissions = transformRoles(user.roles);
  const uniquePermissions: any = buildPermissionList(user.roles);

  const uniqGuid = crypto.randomUUID();

  const token = generateJWT({
    userId: user._id.toString(),
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    allowedApplications: user.allowedApplications,
    uniqSocketId: uniqGuid,
    organizationId: user.organizationId,
    roles: rolesWithoutPermissions,
    permissionList: uniquePermissions,
  });

  const refreshToken = generateRefreshJWT({
    userId: user._id.toString(),
    email: user.email,
    allowedApplications: user.allowedApplications,
    organizationId: user.organizationId,
  });

  const userPayload = await getUserPayload(
    rolesWithoutPermissions,
    uniquePermissions,
    user
  );

  const userToken: userTokens = {
    userId: user._id.toString(),
    accessToken: token,
    refreshToken: refreshToken,
    createdAt: new Date(),
    updatedAt: new Date(),
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
  };

  const tokensCollection = db["connection"].getUserTokensCollection();

  if (isLogin) {
    // 🔄 Login: delete old tokens and insert new
    await tokensCollection.deleteMany({ userId: user._id.toString() });
    await tokensCollection.insertOne(userToken);
  } else {
    // 🔁 Refresh: update accessToken only
    await tokensCollection.updateOne(
      { userId: user._id.toString(), refreshToken: refreshToken.toString() },
      {
        $set: {
          accessToken: token,
          updatedAt: new Date(),
        },
      }
    );
  }

  return { token, userPayload, refreshToken };
};
// Helper to get allowed applications for a user by joining personnel and license collections
const getAllowedApplicationsForUser = async (personnel: User) => {
  // Check if personnel exists and has allowedApplications
  if (!personnel || !Array.isArray(personnel.allowedApplications)) {
    return [];
  }
  // Find licenses for allowed applications
  const licenses = await db["connection"]
    .getLicenseCollection()
    .find({
      applicationName: { $in: personnel.allowedApplications },
    })
    .toArray();
  // Return array of license objects (with applicationName and displayName if available)
  return licenses.map((lic: any) => ({
    applicationName: lic.applicationName,
    displayName: lic.displayName || "",
    redirectUrl: lic.redirectUrl || "",
    description: lic.description || "",
  }));
};

export const login = async (req: Request, res: Response) => {
  try {
    const { email, password } = loginSchema.parse(req.body);

    const user = await getUserByEmail(email);

    if (!user || !(await verifyPassword(password, user.password))) {
      return res.status(401).json({ message: "Invalid credentials" });
    }

    await updateUser(user._id, { lastLogin: new Date() });

    // Get allowed applications from personnel and license collections
    let allowedApplications = await getAllowedApplicationsForUser(user);
    // Remove 'description' field from each allowed application
    allowedApplications = allowedApplications.map(({ description, ...rest }: any) => rest);

    // Assign to user.allowedApplications for downstream use (if user is not readonly)
    if (user && typeof user === "object") {
      (user as any).allowedApplications = allowedApplications;
    }

    const organization = await db["connection"]
      .getOrganizationCollection()
      .findOne({ _id: new ObjectId(user.organizationId) });

    if (!organization) {
      return res.status(404).json({ message: "Organization not found" });
    }

    const isOrgMFAEnabled = organization.is2FAEnabled;
    const orgMFATypes = organization.MFAType ?? [];

    if (isOrgMFAEnabled && orgMFATypes.length > 0) {
      const selectedMFAMethod = orgMFATypes[0] as OTPCodeType;
      const otpCode = generateOTPCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

      await createOTPCode(user._id, otpCode, selectedMFAMethod, expiresAt);

      if (selectedMFAMethod === OTPCodeType.EMAIL) {
        await sendOTPEmail(user.email, otpCode);
      } else if (selectedMFAMethod === OTPCodeType.SMS) {
        // await sendOTPSMS(user.phoneNumber, otpCode);
      }

      return res.json({
        requiresMFA: true,
        method: selectedMFAMethod,
        userId: user._id.toString(),
        totpSecret: user.totpSecret || null,
        message: `Verification code sent to your ${selectedMFAMethod}`,
      });
    }

    // 🚀 Login: create new tokens
    const { token, userPayload, refreshToken } = await createToken(user, true);

    return res.json({
      accessToken: token,
      refreshToken,
      user: userPayload,
    });
  } catch (error) {
    console.error("Login Error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const verifyOTP = async (req: Request, res: Response) => {
  try {
    const { code, method } = otpVerificationSchema.parse(req.body);
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ message: "User ID is required" });
    }

    const user = await getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    let isValid = false;
    const enumMethod = method as OTPCodeType;

    if (enumMethod === OTPCodeType.AUTHENTICATOR && user.totpSecret) {
      isValid = verifyTOTP(code, user.totpSecret);
    } else {
      const otpCode = await getValidOTPCode(userId, code, enumMethod, false);
      if (otpCode) {
        await markOTPCodeUsed(otpCode._id);
        isValid = true;
      }
    }

    if (!isValid) {
      return res.status(401).json({ message: "Invalid verification code" });
    }

    // 🚀 Login: create new tokens
    const { token, userPayload, refreshToken } = await createToken(user, true);

    return res.json({
      accessToken: token,
      refreshToken,
      user: userPayload,
    });
  } catch (error) {
    console.error("Verify OTP Error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const setupTOTP = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const _id = req.params.id;
    const user = await getUser(_id);

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Generate TOTP secret
    const secret = await generateTOTPSecret();
    const qrCode = await generateQRCode(secret, user?.email || "");

    // Save secret to user (not activated until verified)
    await updateUser(ObjectId.createFromHexString(_id), { totpSecret: secret });

    return res.json({
      secret,
      qrCode,
      manualEntryKey: secret,
    });
  } catch (error) {
    console.log("setupTOTP error", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const enableMFA = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { method, code } = req.body;
    const user = req.user!;

    if (method === OTPCodeType.AUTHENTICATOR) {
      if (!user.totpSecret || !verifyTOTP(code, user.totpSecret)) {
        return res.status(401).json({ message: "Invalid authenticator code" });
      }
    }

    await updateUser(user._id, {
      mfaMethod: method,
    });

    return res.json({ message: "MFA enabled successfully" });
  } catch (error) {
    res.status(500).json({ message: "Internal server error" });
  }
};

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email } = forgotPasswordSchema.parse(req.body);

    const user = await getUserByEmail(email);
    if (!user) {
      // Don't reveal whether email exists
      return res.json({
        message: "If the email exists, a temporary password has been sent",
      });
    }

    // Generate temporary password
    const tempPassword = generateTemporaryPassword();
    const hashedTempPassword = await hashPassword(tempPassword);

    // Update user password
    await updateUser(user._id, { password: hashedTempPassword });

    // Send email
    await sendPasswordResetEmail(user.email, tempPassword);

    res.json({
      message: "If the email exists, a temporary password has been sent",
    });
  } catch (error) {
    res.status(500).json({ message: "Internal server error" });
  }
};

export const currentUser = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const _id = req.query.id;
    const user = await getUserById(_id as string);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    return res.json(user);
  } catch (error) {
    res.status(500).json({ message: "Internal server error" });
  }
};

export const logout = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.body.user!;
    if (user) {
      const result = await db["connection"]
        .getUserTokensCollection()
        .deleteMany({ userId: user.id });
      if (result.deletedCount === 0) {
        return res.status(404).json({ message: "User not found" });
      }
      return res.json({ message: "Logged out successfully" });
    }
  } catch (error) {
    console.log(error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const resendOTP = async (req: Request, res: Response) => {
  try {
    const { userId, method } = req.body;

    const user = await getUser(userId);
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (method === OTPCodeType.EMAIL) {
      const otpCode = generateOTPCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      await updateOTPCode(user._id, otpCode, method, expiresAt);

      await sendOTPEmail(user.email, otpCode);
    }

    res.json({ message: "Verification code sent" });
  } catch (error) {
    res.status(500).json({ message: "Internal server error" });
  }
};

export const checkAuthValidity = async (req: Request, res: Response) => {
  try {
    const { refreshToken, application } = req.body;
    if (!refreshToken) {
      return res
        .status(401)
        .json({ success: false, message: "Unauthorized: Token missing" });
    }
    if (!application) {
      return res
        .status(401)
        .json({ success: false, message: "Unauthorized: Application missing" });
    }

    // 1. Verify refresh token
    let payload: any;
    try {
      payload = jwt.verify(refreshToken, process.env.JWT_SECRET!);
    } catch (err) {
      return res
        .status(401)
        .json({ success: false, message: "Invalid or expired refresh token" });
    }

    const { userId, allowedApplications } = payload;

    // Step 2: Validate application access (skip for 'home')
    const skipAppCheck = application === "home";
    const isAllowed =
      Array.isArray(allowedApplications) &&
      allowedApplications.some(
        (app: any) => app.applicationName === application
      );

    if (!skipAppCheck && !isAllowed) {
      return res.status(403).json({
        success: false,
        message:
          "You do not have access to this application. Please contact your administrator.",
      });
    }

    // 3. Find stored token record
    const tokenRecord = await db["connection"]
      .getUserTokensCollection()
      .findOne({
        userId: userId.toString(),
        refreshToken: refreshToken.toString(),
      });

    if (!tokenRecord) {
      return res.status(401).json({
        success: false,
        message: "Refresh token not found or expired",
      });
    }

    let accessTokenValid = false;

    // 4. Validate existing access token
    try {
      const decodedToken = jwt.verify(
        tokenRecord.accessToken,
        process.env.JWT_SECRET!
      );
      if (!decodedToken) {
        accessTokenValid = false;
      }
      accessTokenValid = true;
    } catch (err) {
      accessTokenValid = false;
    }

    let accessToken = tokenRecord.accessToken;

    // 5. If access token expired, create new token pair
    const user = await getUserById(userId);
    if (!accessTokenValid) {
      if (!user) {
        return res
          .status(404)
          .json({ success: false, message: "User not found" });
      }

      const newTokens = await createToken(user, false);
      accessToken = newTokens.token;
    }

    // ✅ NEW permission aggregation logic

    // permissionsList looks like:
    // [
    //   { personnel: ["create", "delete", "update"] },
    //   { organization: ["create", "delete", "update"] }
    // ]


    const rolesWithoutPermissions = transformRoles(user?.roles);
    const uniquePermissions: any = buildPermissionList(user?.roles, user?.selectedPermissions);

    const userPayload = await getUserPayload(
      rolesWithoutPermissions || [],
      uniquePermissions,
      user || undefined
    );

    // ✅ Single return block
    return res.status(200).json({
      success: true,
      message: "Success",
      user: userPayload,
      accessToken,
      refreshToken: refreshToken,
    });
  } catch (error) {
    console.error("checkAuthValidity error:", error);
    res.status(500).json({ success: false, message: "Server error" });
  }
};


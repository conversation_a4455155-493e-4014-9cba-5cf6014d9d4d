/**
 * Socket.IO Client Example
 * This example shows how to connect to and interact with the simplified socket server
 */

// Import socket.io-client (you need to install it: npm install socket.io-client)
// const io = require('socket.io-client');

// Example usage with user ID
class SocketClient {
    constructor(serverUrl, userId) {
        this.serverUrl = serverUrl;
        this.userId = userId;
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    connect() {
        // Connect to server
        this.socket = io(this.serverUrl, {
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        });

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Connection successful
        this.socket.on('connect', () => {
            console.log('✅ Connected to server:', this.socket.id);
            this.isConnected = true;
            this.reconnectAttempts = 0;

            // Join with user ID after connection
            this.joinWithUserId();
        });

        // Handle connection errors
        this.socket.on('connect_error', (error) => {
            console.error('❌ Connection failed:', error.message);
            this.isConnected = false;
            this.handleReconnection();
        });

        // Handle disconnection
        this.socket.on('disconnect', (reason) => {
            console.log('🔌 Disconnected:', reason);
            this.isConnected = false;

            if (reason === 'io server disconnect') {
                // Server disconnected the client, don't reconnect
                console.log('Server disconnected the client');
            } else {
                // Client disconnected, try to reconnect
                this.handleReconnection();
            }
        });

        // User online/offline notifications
        this.socket.on('userOnline', (data) => {
            console.log('🟢 User came online:', data);
        });

        this.socket.on('userOffline', (data) => {
            console.log('🔴 User went offline:', data);
        });

        // Handle errors
        this.socket.on('error', (error) => {
            console.error('⚠️ Socket error:', error);
        });
    }

    // Join with user ID
    joinWithUserId() {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        this.socket.emit('join', { userId: this.userId }, (response) => {
            if (response.success) {
                console.log('✅ Joined successfully:', response);
            } else {
                console.error('❌ Failed to join:', response.message);
            }
        });
    }



    // Logout and disconnect
    logout() {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        this.socket.emit('logout', (response) => {
            if (response.success) {
                console.log('✅ Logged out successfully:', response.message);
            } else {
                console.error('❌ Logout failed:', response.message);
            }
        });
    }

    // Handle reconnection logic
    handleReconnection() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

            console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);

            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('❌ Max reconnection attempts reached. Please refresh the page.');
        }
    }



    // Disconnect from server
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.isConnected = false;
        }
    }
}

// Example usage:
/*
// Initialize client with your user ID
const userId = 'user123';
const client = new SocketClient('http://localhost:5005', userId);

// Connect to server
client.connect();

// Logout when user clicks logout button
document.getElementById('logout-btn').addEventListener('click', () => {
    client.logout();
});
*/

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SocketClient;
}

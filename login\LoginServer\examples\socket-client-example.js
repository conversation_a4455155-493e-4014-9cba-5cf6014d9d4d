/**
 * Socket.IO Client Example
 * This example shows how to connect to and interact with the improved socket server
 */

// Import socket.io-client (you need to install it: npm install socket.io-client)
// const io = require('socket.io-client');

// Example usage with authentication
class SocketClient {
    constructor(serverUrl, authToken) {
        this.serverUrl = serverUrl;
        this.authToken = authToken;
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    connect() {
        // Connect with authentication token
        this.socket = io(this.serverUrl, {
            auth: {
                token: this.authToken
            },
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        });

        this.setupEventListeners();
    }

    setupEventListeners() {
        // Connection successful
        this.socket.on('connect', () => {
            console.log('✅ Connected to server:', this.socket.id);
            this.isConnected = true;
            this.reconnectAttempts = 0;
        });

        // Server confirms connection
        this.socket.on('connected', (data) => {
            console.log('🎉 Connection confirmed:', data);
        });

        // Handle authentication errors
        this.socket.on('connect_error', (error) => {
            console.error('❌ Connection failed:', error.message);
            this.isConnected = false;
            
            if (error.message.includes('Authentication')) {
                console.error('🔒 Authentication failed. Please check your token.');
                return; // Don't retry on auth errors
            }
            
            this.handleReconnection();
        });

        // Handle disconnection
        this.socket.on('disconnect', (reason) => {
            console.log('🔌 Disconnected:', reason);
            this.isConnected = false;
            
            if (reason === 'io server disconnect') {
                // Server disconnected the client, don't reconnect
                console.log('Server disconnected the client');
            } else {
                // Client disconnected, try to reconnect
                this.handleReconnection();
            }
        });

        // Receive messages
        this.socket.on('receiveMessage', (data) => {
            console.log('📨 Message received:', data);
            this.displayMessage(data);
        });

        // User online/offline notifications
        this.socket.on('userOnline', (data) => {
            console.log('🟢 User came online:', data);
        });

        this.socket.on('userOffline', (data) => {
            console.log('🔴 User went offline:', data);
        });

        // Handle errors
        this.socket.on('error', (error) => {
            console.error('⚠️ Socket error:', error);
        });

        // Handle pong responses
        this.socket.on('pong', (data) => {
            console.log('🏓 Pong received:', data);
        });
    }

    // Send a message
    sendMessage(message, targetRoom = null, messageType = 'text') {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        const messageData = {
            message,
            targetRoom,
            messageType
        };

        this.socket.emit('sendMessage', messageData, (response) => {
            if (response.success) {
                console.log('✅ Message sent successfully:', response);
            } else {
                console.error('❌ Failed to send message:', response.message);
            }
        });
    }

    // Join a room
    joinRoom(roomName) {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        this.socket.emit('joinRoom', { room: roomName }, (response) => {
            if (response.success) {
                console.log('✅ Joined room successfully:', response.message);
            } else {
                console.error('❌ Failed to join room:', response.message);
            }
        });
    }

    // Leave a room
    leaveRoom(roomName) {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        this.socket.emit('leaveRoom', { room: roomName }, (response) => {
            if (response.success) {
                console.log('✅ Left room successfully:', response.message);
            } else {
                console.error('❌ Failed to leave room:', response.message);
            }
        });
    }

    // Send ping to check connection
    ping() {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        this.socket.emit('ping', (response) => {
            console.log('🏓 Ping response:', response);
        });
    }

    // Logout and disconnect
    logout() {
        if (!this.isConnected) {
            console.error('❌ Not connected to server');
            return;
        }

        this.socket.emit('logout', (response) => {
            if (response.success) {
                console.log('✅ Logged out successfully:', response.message);
            } else {
                console.error('❌ Logout failed:', response.message);
            }
        });
    }

    // Handle reconnection logic
    handleReconnection() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
            
            console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);
            
            setTimeout(() => {
                this.connect();
            }, delay);
        } else {
            console.error('❌ Max reconnection attempts reached. Please refresh the page.');
        }
    }

    // Display received message (customize this based on your UI)
    displayMessage(data) {
        console.log(`[${data.timestamp}] ${data.sender.firstName} ${data.sender.lastName}: ${data.message}`);
        
        // Example: Add to chat UI
        // const chatContainer = document.getElementById('chat-messages');
        // const messageElement = document.createElement('div');
        // messageElement.innerHTML = `
        //     <div class="message">
        //         <span class="sender">${data.sender.firstName} ${data.sender.lastName}</span>
        //         <span class="timestamp">${new Date(data.timestamp).toLocaleTimeString()}</span>
        //         <div class="content">${data.message}</div>
        //     </div>
        // `;
        // chatContainer.appendChild(messageElement);
    }

    // Disconnect from server
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.isConnected = false;
        }
    }
}

// Example usage:
/*
// Initialize client with your JWT token
const authToken = 'your-jwt-token-here';
const client = new SocketClient('http://localhost:5005', authToken);

// Connect to server
client.connect();

// Send a message after connection
setTimeout(() => {
    client.sendMessage('Hello, everyone!');
}, 2000);

// Join a specific room
setTimeout(() => {
    client.joinRoom('org:your-organization-id');
}, 3000);

// Send ping every 30 seconds to keep connection alive
setInterval(() => {
    client.ping();
}, 30000);

// Logout when user clicks logout button
document.getElementById('logout-btn').addEventListener('click', () => {
    client.logout();
});
*/

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SocketClient;
}
